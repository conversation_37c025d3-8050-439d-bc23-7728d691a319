# Authentication Configuration

## Overview

The app uses environment-specific configuration files, and the authentication flow is implemented via a WebView that communicates completion back to the app using `postMessage`. The server manages redirects (Okta authorize and callback) and returns a small HTML page that posts a structured message back to the WebView.

## Configuration Files

### App Configuration

- `app.config.json` – Application configuration containing auth redirect URLs and other app-specific settings

### Configuration Loading

The configuration is loaded from a single source:

```typescript
import appConfig from '../../app.config.json';
// Always uses the same config file for consistency
```

## Redirects and Completion

- The client starts authentication by opening the middleware route `/auth/login` inside a WebView. A `state` parameter is generated on the client and passed to the server to correlate the flow.
- The middleware handles redirects to the IdP (Okta) and back to its callback route, then renders a response page that posts a JSON message (with `origin` and result payload) back to the WebView.
- The app validates the message origin and state, then exchanges the short-lived token for an API token and proceeds.

Note: `AUTH_REDIRECT_URL` exists in the environment config for future alignment and tooling, but the current client flow relies on the postMessage response from the middleware rather than consuming `AUTH_REDIRECT_URL` directly.

## Benefits of This Approach

1. **Origin-checked postMessage**: Strong, simple completion signaling from middleware to app.
2. **State validation**: The app enforces strict state checks to mitigate CSRF.
3. **Environment clarity**: Config files cleanly separate environment-specific values.
4. **Flexibility**: Easy to extend for staging or other environments.

## AuthService Integration (Best Practices)

The client `AuthService` should:

- Build the middleware login URL (e.g., `/auth/login`) and pass the client-generated `state`.
- Use platform-aware base URLs (Android emulator vs iOS simulator) for local development.
- Avoid hardcoding deep link schemes; prefer the middleware’s postMessage completion.
- Perform strict origin and state validation on messages received in the WebView before exchanging tokens.
- Keep token exchange, storage, and error handling centralized (single responsibility).

Optionally, if you later decide to drive any URL patterns from config:

- Make `AUTH_REDIRECT_URL` the single source for any client-displayed links or diagnostics.
- Ensure documentation and implementation remain aligned (config → usage), and keep tests updated.

## Configuration Management

The app uses a single `app.config.json` file for simplicity and consistency. Environment-specific values are handled through:

1. **Environment variables** (via `.env` files) for API URLs, keys, and debug flags
2. **App config** (`app.config.json`) for auth redirect URLs and app-specific settings
3. **Development config** (`dev.config.json`) for optional development API credentials
