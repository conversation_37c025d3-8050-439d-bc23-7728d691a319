/**
 * Shared types for API services
 *
 * Note: Storage interfaces have been moved to src/types/index.ts
 * Import ISecureStorageRepository from '@/types' instead
 */

/**
 * API Configuration interface
 */
export interface IApiConfigService {
	loadApiConfig(): Promise<{ apiKey: string | null; apiBaseUrl: string | null }>;
	saveApiKey(apiKey: string): Promise<void>;
	saveApiBaseUrl(url: string): Promise<void>;
}

/**
 * API Client configuration options
 */
export interface ApiClientOptions {
	timeout?: number;
	retryAttempts?: number;
	baseUrl?: string; // Override config baseUrl
}

/**
 * API Response wrapper
 */
export interface ApiResponse<T = any> {
	data: T;
	success: boolean;
	message?: string;
}
