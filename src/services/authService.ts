import { ApiFactory } from '@/services/apiFactory';
import { Routes, buildRouteUrl } from '@/api/routes';
import { secureStorageRepository } from '@/repositories/secureStorageRepository';
import type { AuthData } from '@/repositories/secureStorageRepository';
import { Platform } from 'react-native';
import type { ApiResponse } from '@/api/api';
import { createUserService } from '@/services/userService';

export interface AuthService {
	getLoginUrl(state?: string): string;
	generateState(len?: number): string;
	getAllowedOrigin(): string;
	exchangeToken(
		shortToken: string,
	): Promise<{ success: boolean; token?: string; token_type?: string; error?: string }>;
	getToken(): Promise<string | null>;
	logout(): Promise<void>;
	devLogin(email: string): Promise<any>;
}

function toOrigin(url: string): string {
	try {
		const u = new URL(url);
		return `${u.protocol}//${u.host}`;
	} catch {
		// Fallback: best-effort origin extraction
		const m = url.match(/^((https?:)\/\/[^\/]+)\//i);
		return m?.[1] || '';
	}
}

function randomState(len: number = 16): string {
	const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
	let out = '';
	for (let i = 0; i < len; i++) {
		out += alphabet.charAt(Math.floor(Math.random() * alphabet.length));
	}
	return out;
}

class DefaultAuthService implements AuthService {
	private api = ApiFactory.getApiClient();
	private _clearingExpired = false;

	getLoginUrl(state?: string): string {
		const route = Routes.auth.login('mobile', state);

		// For WebView, we need to ensure we're using the right host
		// based on platform to properly connect to the middleware API
		let baseUrl = this.api.baseUrl;

		// For Android, replace localhost with ******** if present
		if (Platform.OS === 'android' && baseUrl.includes('localhost')) {
			baseUrl = baseUrl.replace('localhost', '********');
			if (__DEV__) console.log('Android detected, using emulator special IP:', baseUrl);
		}
		// For iOS simulator, keep localhost as is - shared cookies should handle this
		if (Platform.OS === 'ios' && __DEV__) {
			console.log('iOS detected, using standard localhost');
		}

		// Build the URL
		const url = buildRouteUrl(baseUrl, route);
		if (__DEV__) console.log('Login URL:', url);

		return url;
	}

	generateState(len?: number): string {
		return randomState(len);
	}

	getAllowedOrigin(): string {
		// Extract origin from API URL
		return toOrigin(this.api.baseUrl);
	}

	async exchangeToken(
		shortToken: string,
	): Promise<{ success: boolean; token?: string; token_type?: string; error?: string }> {
		try {
			if (__DEV__) console.log('Exchanging token via ApiClient using Routes.auth.exchangeToken');

			const { data }: ApiResponse<{ token?: string; token_type?: string }> = await this.api.request(
				Routes.auth.exchangeToken(),
				{ token: shortToken },
			);

			const token = (data as any)?.token;

			if (token) {
				await this.persistAuth(String(token));
				return { success: true, token, token_type: 'Bearer' };
			}

			return { success: false, error: 'No token returned from exchange' };
		} catch (e: any) {
			console.error('[AuthScreen] Token exchange failed:', e?.message || 'Unknown error');
			return { success: false, error: e?.message || 'Network error' };
		}
	}

	async getToken(): Promise<string | null> {
		try {
			const auth = await secureStorageRepository.getAuthData();

			// Check token validity by expiration time if available
			if (auth?.token && auth.expiresAt) {
				const now = Date.now();
				if (now >= auth.expiresAt) {
					if (!this._clearingExpired) {
						this._clearingExpired = true;
						if (__DEV__) console.warn('Token expired, clearing authentication data');
						// Clear locally to avoid any potential server call loops
						try {
							await secureStorageRepository.clearAuthData();
						} finally {
							this._clearingExpired = false;
						}
					}
					return null;
				}
			}

			return auth?.token ?? null;
		} catch {
			return null;
		}
	}

	async logout(): Promise<void> {
		try {
			const token = await this.getToken();
			if (token) {
				if (__DEV__) console.log('Logging out via server using Routes.auth.logout');
				try {
					await this.api.request(Routes.auth.logout());
				} catch (e: any) {
					if (__DEV__) console.warn('Server logout failed (continuing with local clear):', e?.message);
				}
			}
		} finally {
			// Always clear local auth data
			try {
				await secureStorageRepository.clearAuthData();
			} catch {
				// no-op: best-effort clear of secure storage; nothing else to do here
			}
		}
	}

	// Dev-only "email-based" login. This must not ship to production and should be guarded by __DEV__.
	async devLogin(email: string): Promise<any> {
		try {
			if (__DEV__) console.log(`Attempting dev login for email: ${email}`);

			// Get the API base URL (now includes platform-specific adjustments)
			if (__DEV__) console.log('Login route:', Routes.auth.devLogin(email));

			const { data }: ApiResponse<{ token?: string }> = await this.api.request(Routes.auth.devLogin(email));

			if (data?.token) {
				// Store the token for future authenticated requests
				await this.persistAuth(data.token);

				// Fetch user profile after login via UserService
				const userService = await createUserService();
				return await userService.getProfile();
			} else {
				throw new Error('No token returned from login');
			}
		} catch (e: any) {
			if (__DEV__) console.error('Dev login failed:', e?.message || 'Unknown error');
			throw new Error(e?.message || 'Login failed');
		}
	}

	private async persistAuth(token: string): Promise<void> {
		const authData: AuthData = {
			token,
			refreshToken: '',
			expiresAt: Date.now() + 60 * 60 * 1000,
		};
		await secureStorageRepository.saveAuthData(authData);
	}
}

export const createAuthService = async (): Promise<AuthService> => {
	return new DefaultAuthService();
};
