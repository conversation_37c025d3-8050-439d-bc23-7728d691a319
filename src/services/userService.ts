import { ApiFactory } from '@/services/apiFactory';
import type { ApiResponse } from '@/api/api';
import { secureStorageRepository } from '@/repositories/secureStorageRepository';
import { Routes } from '@/api/routes';

export interface UserService {
  getProfile(): Promise<any>;
}

class DefaultUserService implements UserService {
  private api = ApiFactory.getApiClient();

  async getProfile(): Promise<any> {
    try {
      if (__DEV__) console.log('[UserService] Fetching user profile via ApiClient using Routes.user.me');

      const { data: userData }: ApiResponse<any> = await this.api.request(Routes.user.me());

      if (__DEV__) {
        console.log('[UserService] User profile fetch response:', userData);
      }

      if (!userData) {
        throw new Error('Failed to fetch user profile');
      }

      return userData;
    } catch (e: any) {
      if (__DEV__)
        console.warn('[UserService] ⚠️ User profile fetch failed:', e?.message || 'Unknown error');

      if (e?.message?.includes('HTTP 401')) {
        try {
          // Clear invalid/expired token to avoid loops
          await secureStorageRepository.clearAuthData();
        } catch {}
        throw new Error('Authentication failed. Please log in again.');
      } else if (e?.message?.includes('HTTP 500')) {
        // Some environments may return 500 instead of 401 when unauthenticated
        // Clear token to allow a clean re-auth flow
        try {
          await secureStorageRepository.clearAuthData();
        } catch {}
        throw new Error('Authentication failed. Please log in again.');
      } else if (e?.message?.includes('HTTP 403')) {
        throw new Error("You don't have permission to access this resource.");
      } else if (
        e?.message?.includes('Network Error') ||
        e?.message?.includes('Failed to fetch')
      ) {
        throw new Error('Network error. Please check your connection and try again.');
      }

      throw new Error('Failed to fetch user profile: ' + (e?.message || 'Unknown error'));
    }
  }
}

export const createUserService = async (): Promise<UserService> => {
  return new DefaultUserService();
};
