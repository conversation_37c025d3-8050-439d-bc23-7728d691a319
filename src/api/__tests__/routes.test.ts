import { buildRouteUrl, type RouteDef } from '@/api/routes';
import { ApiMethods } from '@/types';

describe('buildRouteUrl', () => {
	test('normalizes base URL without double slashes', () => {
		const baseWithSlash = 'https://api.example.com/';
		const baseWithoutSlash = 'https://api.example.com';

		const route: RouteDef = { path: '/auth/login', method: ApiMethods.GET } as any;

		expect(buildRouteUrl(baseWithSlash, route)).toBe('https://api.example.com/auth/login');
		expect(buildRouteUrl(baseWithoutSlash, route)).toBe('https://api.example.com/auth/login');
	});

	test('encodes query parameters correctly (spaces and special chars)', () => {
		const base = 'https://api.example.com/';
		const route: RouteDef = {
			path: '/auth/login',
			method: ApiMethods.GET,
			authenticated: false,
			queryParams: {
				platform: 'mobile',
				state: 'test state with spaces & special chars',
			},
		} as any;

		const url = buildRouteUrl(base, route);
		// Ensure no trailing double slashes and that params are URL-encoded
		expect(url.startsWith('https://api.example.com/auth/login?')).toBe(true);
		const qs = url.split('?')[1];
		expect(qs).toBeTruthy();
		const sp = new URLSearchParams(qs);
		expect(sp.get('platform')).toBe('mobile');
		const stateEncoded = sp.get('state') || '';
		const decodedState = decodeURIComponent(stateEncoded);
		expect(decodedState).toBe('test state with spaces & special chars');
	});

	test('handles function route definitions', () => {
		const base = 'https://api.example.com';
		const routeFactory: RouteDef = ((platform: string, state?: string) => ({
			path: '/auth/login',
			method: ApiMethods.GET,
			authenticated: false,
			queryParams: { platform, ...(state ? { state } : {}) },
		})) as any;

		const url = buildRouteUrl(base, routeFactory as any, 'mobile', 'test-state-123');
		expect(url.startsWith('https://api.example.com/auth/login?')).toBe(true);
		const qs = url.split('?')[1];
		const sp = new URLSearchParams(qs);
		expect(sp.get('platform')).toBe('mobile');
		const stateParam = sp.get('state') || '';
		expect(stateParam).toBe('test-state-123');
	});

	test('no query params returns simple path', () => {
		const base = 'https://api.example.com/';
		const route: RouteDef = { path: '/posts', method: ApiMethods.GET } as any;
		const url = buildRouteUrl(base, route);
		expect(url).toBe('https://api.example.com/posts');
	});
});
