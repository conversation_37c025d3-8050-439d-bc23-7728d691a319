import appConfig from '../../app.config.json';

export interface EnvConfig {
	ENV_NAME: string;
	API_URL: string; // from .env
	X_PUBLIC_KEY: string; // from .env
	DEBUG: boolean; // from .env
	AUTH_REDIRECT_URL: string; // from app.config.json
	CONFIG_VERSION: string; // from app.config.json
	FEATURE_X_ENABLED: boolean; // from app.config.json
}

const getEnv = (): EnvConfig => {
	const extra = process.env || {};

	return {
		ENV_NAME: extra.EXPO_PUBLIC_ENV_NAME ?? 'NO FILE!',
		API_URL: extra.EXPO_PUBLIC_API_URL ?? 'https://fallback-api.example.com',
		X_PUBLIC_KEY: extra.EXPO_PUBLIC_X_PUBLIC_KEY ?? '',
		DEBUG: extra.EXPO_PUBLIC_DEBUG === 'true',
		AUTH_REDIRECT_URL: appConfig.AUTH_REDIRECT_URL ?? 'learningcoachcommunity://auth-callback',
		CONFIG_VERSION: appConfig.CONFIG_VERSION ?? '0.0.0',
		FEATURE_X_ENABLED: appConfig.FEATURE_X_ENABLED ?? false,
	};
};

export default getEnv();
