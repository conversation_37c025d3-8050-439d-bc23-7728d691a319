/**
 * Shared types for API services
 */

/**
 * Secure storage interface for dependency injection
 * Provides both string and object storage capabilities for secure data
 */
export interface ISecureStorageRepository {
	// String operations
	getItem(key: string): Promise<string | null>;
	setItem(key: string, value: string): Promise<void>;
	removeItem(key: string): Promise<void>;
	hasItem(key: string): Promise<boolean>;

	// Object operations (convenience methods with JSON serialization)
	setObject<T>(key: string, value: T): Promise<void>;
	getObject<T>(key: string): Promise<T | null>;
	removeObject(key: string): Promise<void>;

	// Authentication operations
	saveAuthData(authData: any): Promise<void>;
	getAuthData(): Promise<any | null>;
	clearAuthData(): Promise<void>;
	clearAllUserData(): Promise<void>;
}

/**
 * @deprecated Use ISecureStorageRepository instead
 * Kept for backward compatibility during transition
 */
export interface IStorageRepository extends ISecureStorageRepository {}

/**
 * API Configuration interface
 */
export interface IApiConfigService {
	loadApiConfig(): Promise<{ apiKey: string | null; apiBaseUrl: string | null }>;
	saveApiKey(apiKey: string): Promise<void>;
	saveApiBaseUrl(url: string): Promise<void>;
	/**
	 * Normalize a raw base URL for the current platform/environment.
	 * For example, on Android in development, replace localhost with ********.
	 */
	normalizeBaseUrl(raw: string): string;
}

/**
 * API Client configuration options
 */
export interface ApiClientOptions {
	timeout?: number;
	retryAttempts?: number;
	baseUrl?: string; // Override config baseUrl
}

/**
 * API Response wrapper
 */
export interface ApiResponse<T = any> {
	data: T;
	success: boolean;
	message?: string;
}

export enum ApiMethods {
	GET = 'GET',
	POST = 'POST',
	PUT = 'PUT',
	DELETE = 'DELETE',
	PATCH = 'PATCH',
}
