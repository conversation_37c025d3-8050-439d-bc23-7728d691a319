/**
 * User types for UNA CMS integration
 */

export interface UnaUserIds {
	account_id: number;
	content_id: number;
	profile_id: number;
}

export interface UserProfile {
	una_account_id: number;
	una_content_id: number;
	profile_id: number;
	email: string;
	username: string;
	display_name: string;
	role: string;
	status: string;
	avatar_url?: string;
	created_at?: string;
	last_login?: string;
	// Additional UNA-specific fields from actual API response
	email_confirmed?: number;
	phone?: string;
	phone_confirmed?: number;
	receive_updates?: number;
	receive_news?: number;
	lang_id?: number;
	login_attempts?: number;
	locked?: number;
	ip?: string;
	referred?: string;
	[key: string]: any; // Allow for additional UNA fields
}

export interface UserState {
	isInitialized: boolean;
	isAuthenticated: boolean;
	isLoading: boolean;
	user: UserProfile | null;
	token: string | null; // TODO CRM: does this belong here? This is for auth with our api
	error: string | null;
}

export interface LoginCredentials {
	email: string;
}

export interface UnaApiResponse<T> {
	data: T;
	success?: boolean;
	message?: string;
	error?: string;
}
